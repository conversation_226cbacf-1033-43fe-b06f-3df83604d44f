package com.teranew.core.service;

import com.teranew.core.domain.User;
import com.teranew.core.exception.TerasolunaBusinessException;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for User operations.
 * Defines the contract for user-related business operations.
 */
public interface UserService {
    
    /**
     * Creates a new user in the system.
     *
     * @param user the user to create
     * @return the created user with assigned ID
     * @throws TerasolunaBusinessException if user data is invalid or conflicts exist
     */
    User createUser(User user) throws TerasolunaBusinessException;
    
    /**
     * Retrieves a user by their ID.
     * 
     * @param id the user ID
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> getUserById(Long id);
    
    /**
     * Retrieves a user by their username.
     * 
     * @param username the username
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> getUserByUsername(String username);
    
    /**
     * Retrieves a user by their email address.
     * 
     * @param email the email address
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> getUserByEmail(String email);
    
    /**
     * Retrieves all users in the system.
     * 
     * @return a list of all users
     */
    List<User> getAllUsers();
    
    /**
     * Retrieves all active users in the system.
     * 
     * @return a list of active users
     */
    List<User> getActiveUsers();
    
    /**
     * Updates an existing user.
     * 
     * @param user the user with updated information
     * @return the updated user
     * @throws IllegalArgumentException if user data is invalid
     * @throws RuntimeException if user is not found
     */
    User updateUser(User user);
    
    /**
     * Deactivates a user by their ID.
     * 
     * @param id the user ID
     * @throws RuntimeException if user is not found
     */
    void deactivateUser(Long id);
    
    /**
     * Activates a user by their ID.
     * 
     * @param id the user ID
     * @throws RuntimeException if user is not found
     */
    void activateUser(Long id);
    
    /**
     * Permanently deletes a user by their ID.
     * 
     * @param id the user ID
     * @throws RuntimeException if user is not found
     */
    void deleteUser(Long id);
    
    /**
     * Checks if a username is already taken.
     * 
     * @param username the username to check
     * @return true if username exists, false otherwise
     */
    boolean isUsernameExists(String username);
    
    /**
     * Checks if an email is already registered.
     * 
     * @param email the email to check
     * @return true if email exists, false otherwise
     */
    boolean isEmailExists(String email);
}
