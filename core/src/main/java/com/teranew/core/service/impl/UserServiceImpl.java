package com.teranew.core.service.impl;

import com.teranew.core.domain.User;
import com.teranew.core.service.UserService;
import com.teranew.core.exception.TerasolunaBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.terasoluna.gfw.common.exception.SystemException;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * In-memory implementation of UserService for demonstration purposes.
 * In a real application, this would be backed by a database repository.
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    private final Map<Long, User> users = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public UserServiceImpl() {
        // Initialize with some sample data
        createSampleUsers();
    }
    
    private void createSampleUsers() {
        User admin = new User("admin", "<EMAIL>", "Admin", "User");
        admin.setId(idGenerator.getAndIncrement());
        users.put(admin.getId(), admin);
        
        User john = new User("john.doe", "<EMAIL>", "John", "Doe");
        john.setId(idGenerator.getAndIncrement());
        users.put(john.getId(), john);
        
        User jane = new User("jane.smith", "<EMAIL>", "Jane", "Smith");
        jane.setId(idGenerator.getAndIncrement());
        users.put(jane.getId(), jane);
        
        logger.info("Initialized with {} sample users", users.size());
    }
    
    @Override
    public User createUser(User user) {
        logger.info("Creating user: {}", user.getUsername());
        
        if (isUsernameExists(user.getUsername())) {
            throw TerasolunaBusinessException.duplicateUsername(user.getUsername());
        }

        if (isEmailExists(user.getEmail())) {
            throw TerasolunaBusinessException.duplicateEmail(user.getEmail());
        }
        
        user.setId(idGenerator.getAndIncrement());
        users.put(user.getId(), user);
        
        logger.info("Created user with ID: {}", user.getId());
        return user;
    }
    
    @Override
    public Optional<User> getUserById(Long id) {
        logger.debug("Retrieving user by ID: {}", id);
        return Optional.ofNullable(users.get(id));
    }
    
    @Override
    public Optional<User> getUserByUsername(String username) {
        logger.debug("Retrieving user by username: {}", username);
        return users.values().stream()
                .filter(user -> user.getUsername().equals(username))
                .findFirst();
    }
    
    @Override
    public Optional<User> getUserByEmail(String email) {
        logger.debug("Retrieving user by email: {}", email);
        return users.values().stream()
                .filter(user -> user.getEmail().equals(email))
                .findFirst();
    }
    
    @Override
    public List<User> getAllUsers() {
        logger.debug("Retrieving all users");
        return new ArrayList<>(users.values());
    }
    
    @Override
    public List<User> getActiveUsers() {
        logger.debug("Retrieving active users");
        return users.values().stream()
                .filter(User::isActive)
                .toList();
    }
    
    @Override
    public User updateUser(User user) {
        logger.info("Updating user: {}", user.getId());
        
        if (user.getId() == null) {
            throw new IllegalArgumentException("User ID cannot be null for update");
        }
        
        User existingUser = users.get(user.getId());
        if (existingUser == null) {
            throw new RuntimeException("User not found with ID: " + user.getId());
        }
        
        // Check if username or email conflicts with other users
        Optional<User> userWithSameUsername = getUserByUsername(user.getUsername());
        if (userWithSameUsername.isPresent() && !userWithSameUsername.get().getId().equals(user.getId())) {
            throw new IllegalArgumentException("Username already exists: " + user.getUsername());
        }
        
        Optional<User> userWithSameEmail = getUserByEmail(user.getEmail());
        if (userWithSameEmail.isPresent() && !userWithSameEmail.get().getId().equals(user.getId())) {
            throw new IllegalArgumentException("Email already exists: " + user.getEmail());
        }
        
        users.put(user.getId(), user);
        logger.info("Updated user with ID: {}", user.getId());
        return user;
    }
    
    @Override
    public void deactivateUser(Long id) {
        logger.info("Deactivating user: {}", id);
        User user = users.get(id);
        if (user == null) {
            throw new RuntimeException("User not found with ID: " + id);
        }
        
        user.deactivate();
        logger.info("Deactivated user: {}", id);
    }
    
    @Override
    public void activateUser(Long id) {
        logger.info("Activating user: {}", id);
        User user = users.get(id);
        if (user == null) {
            throw new RuntimeException("User not found with ID: " + id);
        }
        
        user.activate();
        logger.info("Activated user: {}", id);
    }
    
    @Override
    public void deleteUser(Long id) {
        logger.info("Deleting user: {}", id);
        User removedUser = users.remove(id);
        if (removedUser == null) {
            throw new RuntimeException("User not found with ID: " + id);
        }
        
        logger.info("Deleted user: {}", id);
    }
    
    @Override
    public boolean isUsernameExists(String username) {
        return users.values().stream()
                .anyMatch(user -> user.getUsername().equals(username));
    }
    
    @Override
    public boolean isEmailExists(String email) {
        return users.values().stream()
                .anyMatch(user -> user.getEmail().equals(email));
    }
}
