package com.teranew.core.exception;

// TERASOLUNA imports - commented out due to compatibility issues with Spring Boot 3.x
// import org.terasoluna.gfw.common.exception.BusinessException;
// import org.terasoluna.gfw.common.message.ResultMessage;
// import org.terasoluna.gfw.common.message.ResultMessages;

/**
 * TERASOLUNA Framework enhanced business exception.
 * Note: Temporarily extends RuntimeException due to TERASOLUNA compatibility issues with Spring Boot 3.x
 * In a full TERASOLUNA setup, this would extend BusinessException
 */
public class TerasolunaBusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Constructor with single error message.
     * 
     * @param message the error message
     */
    public TerasolunaBusinessException(String message) {
        super(ResultMessages.error().add(ResultMessage.fromText(message)));
    }
    
    /**
     * Constructor with error code and message.
     * 
     * @param code the error code
     * @param message the error message
     */
    public TerasolunaBusinessException(String code, String message) {
        super(ResultMessages.error().add(code, message));
    }
    
    /**
     * Constructor with error code and message arguments.
     * 
     * @param code the error code
     * @param args the message arguments
     */
    public TerasolunaBusinessException(String code, Object... args) {
        super(ResultMessages.error().add(code, args));
    }
    
    /**
     * Constructor with ResultMessages.
     * 
     * @param messages the result messages
     */
    public TerasolunaBusinessException(ResultMessages messages) {
        super(messages);
    }
    
    /**
     * Constructor with cause and single error message.
     * 
     * @param message the error message
     * @param cause the cause
     */
    public TerasolunaBusinessException(String message, Throwable cause) {
        super(ResultMessages.error().add(ResultMessage.fromText(message)), cause);
    }
    
    /**
     * Constructor with cause, error code and message.
     * 
     * @param code the error code
     * @param message the error message
     * @param cause the cause
     */
    public TerasolunaBusinessException(String code, String message, Throwable cause) {
        super(ResultMessages.error().add(code, message), cause);
    }
    
    /**
     * Constructor with cause and ResultMessages.
     * 
     * @param messages the result messages
     * @param cause the cause
     */
    public TerasolunaBusinessException(ResultMessages messages, Throwable cause) {
        super(messages, cause);
    }
    
    // Convenience factory methods for common business exceptions
    
    /**
     * Create exception for user not found.
     * 
     * @param userId the user ID
     * @return TerasolunaBusinessException
     */
    public static TerasolunaBusinessException userNotFound(Long userId) {
        return new TerasolunaBusinessException("user.not.found", "User not found with ID: " + userId);
    }
    
    /**
     * Create exception for duplicate username.
     * 
     * @param username the username
     * @return TerasolunaBusinessException
     */
    public static TerasolunaBusinessException duplicateUsername(String username) {
        return new TerasolunaBusinessException("user.username.duplicate", "Username already exists: " + username);
    }
    
    /**
     * Create exception for duplicate email.
     * 
     * @param email the email
     * @return TerasolunaBusinessException
     */
    public static TerasolunaBusinessException duplicateEmail(String email) {
        return new TerasolunaBusinessException("user.email.duplicate", "Email already exists: " + email);
    }
    
    /**
     * Create exception for inactive user.
     * 
     * @param userId the user ID
     * @return TerasolunaBusinessException
     */
    public static TerasolunaBusinessException userInactive(Long userId) {
        return new TerasolunaBusinessException("user.inactive", "User account is inactive: " + userId);
    }
    
    /**
     * Create exception for version conflict (optimistic locking).
     * 
     * @param userId the user ID
     * @return TerasolunaBusinessException
     */
    public static TerasolunaBusinessException versionConflict(Long userId) {
        return new TerasolunaBusinessException("user.version.conflict", "User data has been modified by another user: " + userId);
    }
}
