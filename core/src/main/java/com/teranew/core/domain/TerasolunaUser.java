package com.teranew.core.domain;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
// TERASOLUNA imports - commented out due to compatibility issues with Spring Boot 3.x
// import org.terasoluna.gfw.common.codepoints.CodePoints;
// import org.terasoluna.gfw.common.validator.constraints.ByteMax;
// import org.terasoluna.gfw.common.validator.constraints.ByteMin;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Enhanced User domain model with TERASOLUNA Framework validation features.
 * This class demonstrates TERASOLUNA's advanced validation capabilities.
 */
public class TerasolunaUser {
    
    private Long id;
    
    @NotBlank(message = "{user.username.notblank}")
    @Size(min = 3, max = 50, message = "{user.username.size}")
    // @ByteMin(value = 3, message = "{user.username.bytemin}") - TERASOLUNA validation
    // @ByteMax(value = 50, message = "{user.username.bytemax}") - TERASOLUNA validation
    private String username;

    @NotBlank(message = "{user.email.notblank}")
    @Email(message = "{user.email.invalid}")
    // @ByteMax(value = 254, message = "{user.email.bytemax}") - TERASOLUNA validation
    private String email;

    @NotBlank(message = "{user.firstname.notblank}")
    @Size(max = 100, message = "{user.firstname.size}")
    // @ByteMax(value = 100, message = "{user.firstname.bytemax}") - TERASOLUNA validation
    private String firstName;

    @NotBlank(message = "{user.lastname.notblank}")
    @Size(max = 100, message = "{user.lastname.size}")
    // @ByteMax(value = 100, message = "{user.lastname.bytemax}") - TERASOLUNA validation
    private String lastName;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
    
    private boolean active;
    
    // TERASOLUNA-specific fields
    private String createdBy;
    private String updatedBy;
    private Long version; // For optimistic locking
    
    // Default constructor
    public TerasolunaUser() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.active = true;
        this.version = 1L;
    }
    
    // Constructor with required fields
    public TerasolunaUser(String username, String email, String firstName, String lastName) {
        this();
        this.username = username;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
    }
    
    // Getters and setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public Long getVersion() {
        return version;
    }
    
    public void setVersion(Long version) {
        this.version = version;
    }
    
    // Business methods
    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * TERASOLUNA-specific method to validate username using CodePoints
     * Note: Commented out due to TERASOLUNA compatibility issues with Spring Boot 3.x
     */
    public boolean isValidUsernameCodePoints() {
        if (username == null) {
            return false;
        }
        // Example: Check if username contains only ASCII alphanumeric characters
        // return CodePoints.ASCII_PRINTABLE_CHARS.containsAll(username.codePoints().boxed().toList());

        // Fallback implementation without TERASOLUNA CodePoints
        return username.chars().allMatch(c -> (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_' || c == '-');
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TerasolunaUser user = (TerasolunaUser) o;
        return Objects.equals(id, user.id) && Objects.equals(username, user.username);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, username);
    }
    
    @Override
    public String toString() {
        return "TerasolunaUser{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", active=" + active +
                ", version=" + version +
                '}';
    }
}
