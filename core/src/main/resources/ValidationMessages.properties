# TERASOLUNA Framework Validation Messages
# User validation messages

# Username validation
user.username.notblank=Username is required
user.username.size=Username must be between {min} and {max} characters
user.username.bytemin=Username must be at least {value} bytes
user.username.bytemax=Username must not exceed {value} bytes

# Email validation
user.email.notblank=Email is required
user.email.invalid=Email format is invalid
user.email.bytemax=Email must not exceed {value} bytes

# First name validation
user.firstname.notblank=First name is required
user.firstname.size=First name must not exceed {max} characters
user.firstname.bytemax=First name must not exceed {value} bytes

# Last name validation
user.lastname.notblank=Last name is required
user.lastname.size=Last name must not exceed {max} characters
user.lastname.bytemax=Last name must not exceed {value} bytes

# TERASOLUNA Framework standard error codes
e.xx.fw.5001=Requested resource is not found.
e.xx.fw.7001=Invalid transaction token.
e.xx.fw.8001=Business error occurred.
e.xx.fw.9001=System error occurred.
e.xx.fw.9999=An unexpected error occurred.

# Business logic error messages
user.username.duplicate=Username already exists
user.email.duplicate=Email address already exists
user.not.found=User not found
user.inactive=User account is inactive
user.version.conflict=User data has been modified by another user
