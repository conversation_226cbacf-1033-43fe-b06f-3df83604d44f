package com.teranew.core.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.assertj.core.api.Assertions.*;

import java.time.LocalDateTime;

/**
 * Unit tests for the User domain model.
 */
class UserTest {
    
    private User user;
    
    @BeforeEach
    void setUp() {
        user = new User("testuser", "<EMAIL>", "John", "Doe");
    }
    
    @Test
    void shouldCreateUserWithRequiredFields() {
        assertThat(user.getUsername()).isEqualTo("testuser");
        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
        assertThat(user.getFirstName()).isEqualTo("John");
        assertThat(user.getLastName()).isEqualTo("Doe");
        assertThat(user.isActive()).isTrue();
        assertThat(user.getCreatedAt()).isNotNull();
        assertThat(user.getUpdatedAt()).isNotNull();
    }
    
    @Test
    void shouldReturnFullName() {
        String fullName = user.getFullName();
        assertThat(fullName).isEqualTo("John Doe");
    }
    
    @Test
    void shouldDeactivateUser() {
        LocalDateTime beforeDeactivation = user.getUpdatedAt();
        
        user.deactivate();
        
        assertThat(user.isActive()).isFalse();
        assertThat(user.getUpdatedAt()).isAfter(beforeDeactivation);
    }
    
    @Test
    void shouldActivateUser() {
        user.deactivate();
        LocalDateTime beforeActivation = user.getUpdatedAt();
        
        user.activate();
        
        assertThat(user.isActive()).isTrue();
        assertThat(user.getUpdatedAt()).isAfter(beforeActivation);
    }
    
    @Test
    void shouldUpdateTimestampWhenSettingUsername() {
        LocalDateTime beforeUpdate = user.getUpdatedAt();
        
        user.setUsername("newusername");
        
        assertThat(user.getUsername()).isEqualTo("newusername");
        assertThat(user.getUpdatedAt()).isAfter(beforeUpdate);
    }
    
    @Test
    void shouldUpdateTimestampWhenSettingEmail() {
        LocalDateTime beforeUpdate = user.getUpdatedAt();
        
        user.setEmail("<EMAIL>");
        
        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
        assertThat(user.getUpdatedAt()).isAfter(beforeUpdate);
    }
    
    @Test
    void shouldBeEqualWhenSameIdAndUsername() {
        User user1 = new User("testuser", "<EMAIL>", "John", "Doe");
        User user2 = new User("testuser", "<EMAIL>", "Jane", "Smith");
        user1.setId(1L);
        user2.setId(1L);
        
        assertThat(user1).isEqualTo(user2);
        assertThat(user1.hashCode()).isEqualTo(user2.hashCode());
    }
    
    @Test
    void shouldNotBeEqualWhenDifferentUsername() {
        User user1 = new User("testuser1", "<EMAIL>", "John", "Doe");
        User user2 = new User("testuser2", "<EMAIL>", "John", "Doe");
        user1.setId(1L);
        user2.setId(1L);
        
        assertThat(user1).isNotEqualTo(user2);
    }
    
    @Test
    void shouldHaveStringRepresentation() {
        user.setId(1L);
        String toString = user.toString();
        
        assertThat(toString).contains("User{");
        assertThat(toString).contains("id=1");
        assertThat(toString).contains("username='testuser'");
        assertThat(toString).contains("email='<EMAIL>'");
        assertThat(toString).contains("active=true");
    }
}
