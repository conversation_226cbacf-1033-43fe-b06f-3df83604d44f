plugins {
    java
    `java-library`
}

description = "Core business logic and domain models"

dependencies {
    // Spring Framework (for annotations like @Service)
    implementation("org.springframework:spring-context:6.1.8")

    // Core utilities
    implementation("org.apache.commons:commons-lang3:3.14.0")
    implementation("com.fasterxml.jackson.core:jackson-core:2.17.1")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.17.1")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.17.1")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1")

    // Validation
    implementation("jakarta.validation:jakarta.validation-api:3.0.2")
    implementation("org.hibernate.validator:hibernate-validator:8.0.1.Final")

    // Logging (already included from parent)
    // implementation("org.slf4j:slf4j-api") - inherited from parent

    // Test dependencies (inherited from parent)
    // testImplementation("org.junit.jupiter:junit-jupiter")
    // testImplementation("org.mockito:mockito-core")
    // testImplementation("org.assertj:assertj-core")
}

java {
    withJavadocJar()
    withSourcesJar()
}
