plugins {
    java
    `java-library`
    id("io.spring.dependency-management")
}

description = "Core business logic and domain models with TERASOLUNA Framework"

dependencyManagement {
    imports {
        // TERASOLUNA Framework BOM
        mavenBom("org.terasoluna.gfw:terasoluna-gfw-dependencies:5.10.0.RELEASE")
    }
}

dependencies {
    // TERASOLUNA Framework core dependencies (inherited from parent)
    // implementation("org.terasoluna.gfw:terasoluna-gfw-common") - inherited from parent
    // implementation("org.terasoluna.gfw:terasoluna-gfw-string") - inherited from parent
    // implementation("org.terasoluna.gfw:terasoluna-gfw-codepoints") - inherited from parent
    // implementation("org.terasoluna.gfw:terasoluna-gfw-validator") - inherited from parent

    // Additional TERASOLUNA utilities for core module (using available components)
    // Note: Some TERASOLUNA components may not be available in 5.10.0.RELEASE
    // implementation("org.terasoluna.gfw:terasoluna-gfw-jodatime") - not available
    // implementation("org.terasoluna.gfw:terasoluna-gfw-message") - not available

    // Spring Framework (for annotations like @Service)
    implementation("org.springframework:spring-context")

    // Core utilities - versions managed by TERASOLUNA BOM
    implementation("org.apache.commons:commons-lang3")
    implementation("com.fasterxml.jackson.core:jackson-core")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    // Validation - versions managed by TERASOLUNA BOM
    implementation("jakarta.validation:jakarta.validation-api")
    implementation("org.hibernate.validator:hibernate-validator")

    // Logging (already included from parent)
    // implementation("org.slf4j:slf4j-api") - inherited from parent

    // Test dependencies (inherited from parent)
    // testImplementation("org.junit.jupiter:junit-jupiter")
    // testImplementation("org.mockito:mockito-core")
    // testImplementation("org.assertj:assertj-core")
}

java {
    withJavadocJar()
    withSourcesJar()
}
