plugins {
    java
    id("org.springframework.boot") version "3.3.0" apply false
    id("io.spring.dependency-management") version "1.1.5" apply false
}

allprojects {
    group = "com.teranew"
    version = "1.0.0"
    
    repositories {
        mavenCentral()
    }
}

subprojects {
    apply(plugin = "java")
    apply(plugin = "io.spring.dependency-management")
    
    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(21))
        }
    }
    
    dependencies {
        // Common dependencies for all modules
        implementation("org.terasoluna.gfw:terasoluna-gfw:5.10.0.RELEASE")
        implementation("org.slf4j:slf4j-api:2.0.13")
        testImplementation("org.junit.jupiter:junit-jupiter:5.10.2")
        testImplementation("org.mockito:mockito-core:5.12.0")
        testImplementation("org.assertj:assertj-core:3.26.0")
    }
    
    tasks.test {
        useJUnitPlatform()
    }
    
    tasks.withType<JavaCompile> {
        options.encoding = "UTF-8"
        options.compilerArgs.addAll(listOf(
            "--enable-preview",
            "-Xlint:unchecked",
            "-Xlint:deprecation"
        ))
    }
}
