plugins {
    java
    id("org.springframework.boot") version "3.3.6" apply false
    id("io.spring.dependency-management") version "1.1.6" apply false
}

allprojects {
    group = "com.teranew"
    version = "1.0.0"

    repositories {
        mavenCentral()
        // TERASOLUNA framework repositories
        maven {
            url = uri("https://repo.spring.io/milestone")
        }
        maven {
            url = uri("https://oss.sonatype.org/content/repositories/snapshots")
        }
    }
}

// TERASOLUNA Framework version management
extra["terasolunaVersion"] = "5.10.0.RELEASE"
extra["springFrameworkVersion"] = "6.1.8"
extra["springSecurityVersion"] = "6.3.0"

subprojects {
    apply(plugin = "java")
    apply(plugin = "io.spring.dependency-management")

    java {
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(21))
        }
    }

    // TERASOLUNA Framework BOM will be applied in individual modules
    
    dependencies {
        // TERASOLUNA Framework core dependencies - only include basic ones for now
        implementation("org.terasoluna.gfw:terasoluna-gfw-common")

        // Common dependencies for all modules
        implementation("org.slf4j:slf4j-api:2.0.13")
        testImplementation("org.junit.jupiter:junit-jupiter:5.10.2")
        testImplementation("org.mockito:mockito-core:5.12.0")
        testImplementation("org.assertj:assertj-core:3.26.0")
    }
    
    tasks.test {
        useJUnitPlatform()
    }
    
    tasks.withType<JavaCompile> {
        options.encoding = "UTF-8"
        options.compilerArgs.addAll(listOf(
            "--enable-preview",
            "-Xlint:unchecked",
            "-Xlint:deprecation"
        ))
    }
}
