# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IDE
.idea/
*.iws
*.iml
*.ipr
out/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java
*.class
*.log
*.ctxt
.mtj.tmp/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# Spring Boot
spring-boot-*.log
application-*.properties
application-*.yml

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Node.js (if using frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Maven (if mixed with <PERSON><PERSON>)
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar
