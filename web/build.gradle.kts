plugins {
    java
    id("org.springframework.boot")
    id("io.spring.dependency-management")
}

description = "Web application layer with TERASOLUNA Framework"

dependencyManagement {
    imports {
        // TERASOLUNA Framework BOM
        mavenBom("org.terasoluna.gfw:terasoluna-gfw-dependencies:5.10.0.RELEASE")
    }
}

dependencies {
    // Project dependencies
    implementation(project(":core"))
    implementation(project(":api"))

    // TERASOLUNA Framework web dependencies - basic ones only
    implementation("org.terasoluna.gfw:terasoluna-gfw-web")

    // Spring Boot starters - versions managed by TERASOLUNA BOM where possible
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    
    // Frontend integration
    implementation("org.webjars:bootstrap:5.3.3")
    implementation("org.webjars:jquery:3.7.1")
    implementation("org.webjars.npm:htmx.org:1.9.12")
    
    // Development tools
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    
    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
}

tasks.test {
    useJUnitPlatform()
}
