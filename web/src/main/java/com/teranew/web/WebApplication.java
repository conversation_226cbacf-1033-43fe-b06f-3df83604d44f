package com.teranew.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * Main Spring Boot application class for the Web module.
 * This class bootstraps the Spring Boot web application.
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.teranew.core", "com.teranew.api", "com.teranew.web"})
@EntityScan(basePackages = {"com.teranew.core.domain"})
public class WebApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
