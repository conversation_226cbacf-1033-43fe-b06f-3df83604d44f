package com.teranew.web.controller;

import com.teranew.core.domain.User;
import com.teranew.core.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * Web Controller for User management through web interface.
 * Handles web requests and returns Thymeleaf templates.
 */
@Controller
@RequestMapping("/users")
public class WebUserController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebUserController.class);
    
    private final UserService userService;
    
    @Autowired
    public WebUserController(UserService userService) {
        this.userService = userService;
    }
    
    /**
     * Display list of all users
     */
    @GetMapping
    public String listUsers(Model model) {
        logger.info("Displaying user list page");
        List<User> users = userService.getAllUsers();
        model.addAttribute("users", users);
        return "users/list";
    }
    
    /**
     * Display user details
     */
    @GetMapping("/{id}")
    public String viewUser(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes) {
        logger.info("Displaying user details for ID: {}", id);
        Optional<User> userOpt = userService.getUserById(id);
        
        if (userOpt.isPresent()) {
            model.addAttribute("user", userOpt.get());
            return "users/view";
        } else {
            redirectAttributes.addFlashAttribute("error", "User not found");
            return "redirect:/users";
        }
    }
    
    /**
     * Display create user form
     */
    @GetMapping("/new")
    public String showCreateForm(Model model) {
        logger.info("Displaying create user form");
        model.addAttribute("user", new User());
        return "users/create";
    }
    
    /**
     * Process create user form submission
     */
    @PostMapping
    public String createUser(@Valid @ModelAttribute User user, 
                           BindingResult bindingResult, 
                           Model model, 
                           RedirectAttributes redirectAttributes) {
        logger.info("Processing create user form for username: {}", user.getUsername());
        
        if (bindingResult.hasErrors()) {
            model.addAttribute("user", user);
            return "users/create";
        }
        
        try {
            // Check for existing username or email
            if (userService.isUsernameExists(user.getUsername())) {
                bindingResult.rejectValue("username", "error.user", "Username already exists");
                model.addAttribute("user", user);
                return "users/create";
            }
            
            if (userService.isEmailExists(user.getEmail())) {
                bindingResult.rejectValue("email", "error.user", "Email already exists");
                model.addAttribute("user", user);
                return "users/create";
            }
            
            User createdUser = userService.createUser(user);
            redirectAttributes.addFlashAttribute("success", "User created successfully");
            return "redirect:/users/" + createdUser.getId();
            
        } catch (Exception e) {
            logger.error("Error creating user: {}", e.getMessage());
            model.addAttribute("error", "Error creating user: " + e.getMessage());
            model.addAttribute("user", user);
            return "users/create";
        }
    }
    
    /**
     * Display edit user form
     */
    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes) {
        logger.info("Displaying edit user form for ID: {}", id);
        Optional<User> userOpt = userService.getUserById(id);
        
        if (userOpt.isPresent()) {
            model.addAttribute("user", userOpt.get());
            return "users/edit";
        } else {
            redirectAttributes.addFlashAttribute("error", "User not found");
            return "redirect:/users";
        }
    }
    
    /**
     * Process edit user form submission
     */
    @PostMapping("/{id}")
    public String updateUser(@PathVariable Long id,
                           @Valid @ModelAttribute User user,
                           BindingResult bindingResult,
                           Model model,
                           RedirectAttributes redirectAttributes) {
        logger.info("Processing update user form for ID: {}", id);
        
        if (bindingResult.hasErrors()) {
            user.setId(id);
            model.addAttribute("user", user);
            return "users/edit";
        }
        
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            redirectAttributes.addFlashAttribute("success", "User updated successfully");
            return "redirect:/users/" + updatedUser.getId();
            
        } catch (Exception e) {
            logger.error("Error updating user: {}", e.getMessage());
            model.addAttribute("error", "Error updating user: " + e.getMessage());
            user.setId(id);
            model.addAttribute("user", user);
            return "users/edit";
        }
    }
    
    /**
     * Deactivate user
     */
    @PostMapping("/{id}/deactivate")
    public String deactivateUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        logger.info("Deactivating user with ID: {}", id);
        
        try {
            userService.deactivateUser(id);
            redirectAttributes.addFlashAttribute("success", "User deactivated successfully");
        } catch (Exception e) {
            logger.error("Error deactivating user: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", "Error deactivating user: " + e.getMessage());
        }
        
        return "redirect:/users/" + id;
    }
    
    /**
     * Activate user
     */
    @PostMapping("/{id}/activate")
    public String activateUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        logger.info("Activating user with ID: {}", id);
        
        try {
            userService.activateUser(id);
            redirectAttributes.addFlashAttribute("success", "User activated successfully");
        } catch (Exception e) {
            logger.error("Error activating user: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", "Error activating user: " + e.getMessage());
        }
        
        return "redirect:/users/" + id;
    }
    
    /**
     * Delete user
     */
    @PostMapping("/{id}/delete")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        logger.info("Deleting user with ID: {}", id);
        
        try {
            userService.deleteUser(id);
            redirectAttributes.addFlashAttribute("success", "User deleted successfully");
            return "redirect:/users";
        } catch (Exception e) {
            logger.error("Error deleting user: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", "Error deleting user: " + e.getMessage());
            return "redirect:/users/" + id;
        }
    }
}
