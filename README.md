# Back-End Teranew - Multi-Module Java 21 Project

A modern Gradle multi-module Java 21 project with Spring Boot, demonstrating best practices for enterprise application development.

## Project Structure

This project is organized into three main modules:

### 📦 Core Module (`core/`)
**Purpose**: Contains the core business logic, domain models, and service interfaces.

- **Domain Models**: User entity with validation annotations
- **Service Interfaces**: Business logic contracts (UserService)
- **Utilities**: Common utilities and shared components
- **Dependencies**: Jackson, Jakarta Validation, Hibernate Validator, Apache Commons Lang

### 🌐 API Module (`api/`)
**Purpose**: REST API layer built with Spring Boot, providing HTTP endpoints.

- **REST Controllers**: UserController with OpenAPI documentation
- **Configuration**: Spring Boot auto-configuration
- **Database**: H2 (development) and PostgreSQL (production) support
- **Documentation**: Swagger UI integration
- **Dependencies**: Spring Boot Web, JPA, Validation, Actuator, OpenAPI

### 🖥️ Web Module (`web/`)
**Purpose**: Web application layer with Thymeleaf templates and Spring Security.

- **Web Controllers**: WebUserController for web interface
- **Security**: Spring Security integration
- **Frontend**: Bootstrap, jQuery, HTMX integration
- **Templates**: Thymeleaf for server-side rendering
- **Dependencies**: Spring Boot Web, Thymeleaf, Security, WebJars

## Technology Stack

- **Java**: 21 (with preview features enabled)
- **Gradle**: 8.14 with Kotlin DSL
- **Spring Boot**: 3.3.0
- **Database**: H2 (dev), PostgreSQL (prod)
- **Testing**: JUnit 5, Mockito, AssertJ
- **Documentation**: OpenAPI 3 / Swagger UI
- **Frontend**: Bootstrap 5, jQuery, HTMX

## Getting Started

### Prerequisites
- Java 21 or higher
- Git

### Building the Project

```bash
# Clone the repository
git clone <repository-url>
cd back-end-teranew

# Build all modules
./gradlew build

# Run tests
./gradlew test

# Clean build
./gradlew clean build
```

### Running the Applications

#### API Module (REST API)
```bash
./gradlew :api:bootRun
```
- Application runs on: http://localhost:8080
- Swagger UI: http://localhost:8080/swagger-ui.html
- H2 Console: http://localhost:8080/h2-console

#### Web Module (Web Application)
```bash
./gradlew :web:bootRun
```
- Application runs on: http://localhost:8080
- User management interface available

### Module Dependencies

```
web → api → core
```

- **web** depends on both **api** and **core**
- **api** depends on **core**
- **core** is independent

## Configuration

### Development Profile (default)
- Database: H2 in-memory
- Logging: DEBUG level
- H2 Console: Enabled

### Production Profile
- Database: PostgreSQL
- Logging: INFO level
- Security: Enhanced

## API Endpoints

### User Management
- `GET /api/v1/users` - List all users
- `GET /api/v1/users/{id}` - Get user by ID
- `GET /api/v1/users/username/{username}` - Get user by username
- `POST /api/v1/users` - Create new user
- `PUT /api/v1/users/{id}` - Update user
- `PATCH /api/v1/users/{id}/activate` - Activate user
- `PATCH /api/v1/users/{id}/deactivate` - Deactivate user
- `DELETE /api/v1/users/{id}` - Delete user

## Testing

The project includes comprehensive unit tests:

```bash
# Run all tests
./gradlew test

# Run tests for specific module
./gradlew :core:test
./gradlew :api:test
./gradlew :web:test

# Generate test reports
./gradlew test jacocoTestReport
```

## Development

### Code Style
- Java 21 features enabled
- Preview features allowed
- UTF-8 encoding
- Comprehensive validation

### Build Features
- Gradle build cache enabled
- Parallel execution enabled
- Incremental compilation
- Source and Javadoc JARs generated

## Monitoring

### Actuator Endpoints
- Health: `/actuator/health`
- Info: `/actuator/info`
- Metrics: `/actuator/metrics`
- Prometheus: `/actuator/prometheus`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
