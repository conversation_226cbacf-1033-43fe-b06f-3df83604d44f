package com.teranew.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

/**
 * Main Spring Boot application class for the API module.
 * This class bootstraps the Spring Boot application and configures component scanning.
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.teranew.core", "com.teranew.api"})
@EntityScan(basePackages = {"com.teranew.core.domain"})
public class ApiApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
    }
}
