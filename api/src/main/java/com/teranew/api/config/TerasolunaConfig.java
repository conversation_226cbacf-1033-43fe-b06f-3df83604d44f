package com.teranew.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.terasoluna.gfw.common.exception.ExceptionCodeResolver;
import org.terasoluna.gfw.common.exception.ExceptionLogger;
import org.terasoluna.gfw.common.exception.SimpleMappingExceptionCodeResolver;
import org.terasoluna.gfw.web.exception.SystemExceptionResolver;
import org.terasoluna.gfw.web.logging.TraceLoggingInterceptor;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * TERASOLUNA Framework configuration for API module.
 * This configuration integrates TERASOLUNA framework components with Spring Boot.
 */
@Configuration
@EnableAspectJAutoProxy
public class TerasolunaConfig implements WebMvcConfigurer {

    /**
     * Configure TERASOLUNA exception code resolver.
     * Maps exception types to specific error codes.
     */
    @Bean
    public ExceptionCodeResolver exceptionCodeResolver() {
        SimpleMappingExceptionCodeResolver resolver = new SimpleMappingExceptionCodeResolver();
        
        Map<String, String> exceptionMappings = new LinkedHashMap<>();
        exceptionMappings.put("ResourceNotFoundException", "e.xx.fw.5001");
        exceptionMappings.put("InvalidTransactionTokenException", "e.xx.fw.7001");
        exceptionMappings.put("BusinessException", "e.xx.fw.8001");
        exceptionMappings.put("SystemException", "e.xx.fw.9001");
        exceptionMappings.put("Exception", "e.xx.fw.9999");
        
        resolver.setExceptionMappings(exceptionMappings);
        resolver.setDefaultExceptionCode("e.xx.fw.9999");
        
        return resolver;
    }

    /**
     * Configure TERASOLUNA exception logger.
     * Provides structured logging for exceptions.
     */
    @Bean
    public ExceptionLogger exceptionLogger() {
        ExceptionLogger logger = new ExceptionLogger();
        return logger;
    }

    /**
     * Configure TERASOLUNA system exception resolver.
     * Handles system-level exceptions and provides appropriate responses.
     */
    @Bean
    public SystemExceptionResolver systemExceptionResolver() {
        SystemExceptionResolver resolver = new SystemExceptionResolver();
        resolver.setExceptionCodeResolver(exceptionCodeResolver());
        resolver.setOrder(3);
        
        // Configure default error views for different exception types
        Map<String, String> exceptionMappings = new LinkedHashMap<>();
        exceptionMappings.put("ResourceNotFoundException", "common/error/resourceNotFoundError");
        exceptionMappings.put("BusinessException", "common/error/businessError");
        exceptionMappings.put("InvalidTransactionTokenException", "common/error/transactionTokenError");
        exceptionMappings.put("Exception", "common/error/systemError");
        
        resolver.setExceptionMappings(exceptionMappings);
        resolver.setDefaultErrorView("common/error/systemError");
        
        return resolver;
    }

    /**
     * Configure TERASOLUNA trace logging interceptor.
     * Provides request/response logging for debugging and monitoring.
     */
    @Bean
    public TraceLoggingInterceptor traceLoggingInterceptor() {
        TraceLoggingInterceptor interceptor = new TraceLoggingInterceptor();
        interceptor.setWarnHandlingNanos(1000000000L); // 1 second
        return interceptor;
    }
}
