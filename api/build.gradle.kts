plugins {
    java
    id("org.springframework.boot")
    id("io.spring.dependency-management")
}

description = "REST API layer with Spring Boot and TERASOLUNA Framework"

dependencyManagement {
    imports {
        // TERASOLUNA Framework BOM
        mavenBom("org.terasoluna.gfw:terasoluna-gfw-dependencies:5.10.0.RELEASE")
    }
}

dependencies {
    // Project dependencies
    implementation(project(":core"))

    // TERASOLUNA Framework web dependencies - basic ones only
    implementation("org.terasoluna.gfw:terasoluna-gfw-web")

    // Spring Boot starters - versions managed by TERASOLUNA BOM where possible
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    
    // TERASOLUNA Database support - will be added later when available

    // Database - versions managed by TERASOLUNA BOM where possible
    runtimeOnly("com.h2database:h2") // For development/testing
    runtimeOnly("org.postgresql:postgresql") // For production

    // Documentation
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0")
    
    // Development tools
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    
    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.boot:spring-boot-testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
}

tasks.test {
    useJUnitPlatform()
}
